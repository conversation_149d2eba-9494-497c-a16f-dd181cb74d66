package com.wexl.retail.repository;

import com.wexl.retail.model.User;
import com.wexl.retail.organization.dto.MetricsCountByOrg;
import com.wexl.retail.user.dto.OrganizationStrength;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {

  User findByAuthUserIdAndOrganization(String authUserId, String orgSlug);

  @Query("SELECT obj from User obj where obj.authUserId =:authUserId and obj.isDeleted is NULL")
  User getUserByAuthUserId(@Param("authUserId") String authUserId);

  @Query(
      "SELECT obj from User obj where LOWER(obj.userName) = LOWER(:userName) and obj.isDeleted is NULL")
  User getUserByUserName(@Param("userName") String userName);

  @Query(
      """
      SELECT u from User u \
      where u.deletedAt is null and u.isDeleted is null and u.id in (:userIds)\
      """)
  List<User> fetchStudentsByIds(@Param("userIds") List<Long> userIds);

  @Query(
      value =
          """
                      select u.* from users u
                      inner join teacher_details td on u.id = td.user_id
                      inner join role_templates rt on rt.id = td.role_template_id
                      where u.organization =:orgSlug and rt.template  = 'ADMIN'
                      order by u.created_at asc limit 1
                      """,
      nativeQuery = true)
  User getOrgAdminForOrganization(String orgSlug);

  @Query(value = "SELECT *  FROM users  WHERE LOWER(email) = LOWER(:email)", nativeQuery = true)
  List<User> findByEmailWithIgnoreCase(String email);

  Optional<User> findUserByEmail(String email);

  Optional<User> findByEmailAndOrganization(String email, String organization);

  Optional<User> findByAuthUserId(String authUserId);

  boolean existsByUserName(String userName);

  List<User> findUsersByMobileNumber(String mobileNumber);

  @Query(
      "SELECT count(obj) from User obj where obj.organization =:orgSlug and obj.id in (:userIds)")
  long countOfStudentsBelongingToOrg(
      @Param("orgSlug") String orgSlug, @Param("userIds") List<Long> userIds);

  @Query(
      value =
          """
          select u.* from users u\
           inner join teacher_details td on u.id=td.user_id\
           inner join user_roles ur on u.id = ur.user_id\
           inner join roles r on ur.role_id = r.id\
           where r.name='ROLE_TEACHER' and (LOWER(concat(u.first_name, ' ', u.last_name)) \
          like %:searchTerm% or LOWER(td.teacher_code) like %:searchTerm%)\
          and td.teacher_code not in (:excludedTeacherCodes) \
          and u.deleted_at is null limit 5\
          """,
      nativeQuery = true)
  List<User> getTeacherBySearchTerm(
      @Param("searchTerm") String searchTerm,
      @Param("excludedTeacherCodes") List<String> excludedTeacherCodes);

  @Query(
      value =
          """
          select u.id from users u
          inner join students s on u.id = s.user_id where s.class_id=:classId
          and (cast((:boardIds) as varChar) is null or s.board_id in (:boardIds))
          and u.organization=:organization and u.deleted_at is null
          """,
      nativeQuery = true)
  List<Long> findAllUsersByGradeAndOrganization(
      long classId, String organization, List<Integer> boardIds);

  @Transactional
  @Modifying
  @Query(
      value =
          """
          update users
          set last_login = :currentDate,
          guid = :guid
          where auth_user_id = :authUserId\
          """,
      nativeQuery = true)
  void updateUserLastLoginAndGuid(Date currentDate, String guid, String authUserId);

  @Transactional
  @Modifying
  @Query(
      value =
          """
          update users
          set firebase_token = :firebaseToken
          where auth_user_id = :authUserId\
          """,
      nativeQuery = true)
  void updateUserFirebaseTokens(String firebaseToken, String authUserId);

  @Query(value = "select purge_teacher(:teacherAuthId, :orgSlug)", nativeQuery = true)
  String deleteTeacher(String teacherAuthId, String orgSlug);

  @Query(value = "select purge_student(:studentAuthId)", nativeQuery = true)
  String deleteStudent(String studentAuthId);

  @Query(
      value =
          """
              select count(*) from students s
              inner join users u on u.id = s.user_id
              where u.organization in (:orgSlugs) and u.deleted_at is null""",
      nativeQuery = true)
  Integer getStudentsCountOfOrg(List<String> orgSlugs);

  @Query(
      value =
          """
              select count(*) from teacher_details td
              inner join users u on u.id = td.user_id
              where u.organization in (:orgSlugs) and u.deleted_at is null
                      """,
      nativeQuery = true)
  Integer getTeachersCountOfOrg(List<String> orgSlugs);

  @Query(
      value =
          """
                  select count(td.*)
                  from  teacher_details td  \s
                  join users u on td.user_id = u.id
                  join orgs o on o.slug = u.organization\s
                  where o.deleted_at is null and u.deleted_at is null and td.deleted_at is null  \s

           \
          """,
      nativeQuery = true)
  Integer getTeacherCountOfAllOrgs();

  @Query(
      value =
          """
                          select o.slug as orgSlug, count(td.id) as count from teacher_details td\s
                          inner join users u on u.id = td.user_id\s
                          inner join orgs o on o.slug = u.organization\s
                          where u.organization in (:orgSlug) and  u.deleted_at is null
                          group by o.slug\s
                                     """,
      nativeQuery = true)
  List<MetricsCountByOrg> getTeacherCountByOrgSlug(List<String> orgSlug);

  @Query(
      value =
          """
                                  select o.slug as orgSlug, count(s.id) as count from students s\s
                                  inner join users u on u.id = s.user_id\s
                                  inner join orgs o on o.slug = u.organization\s
                                  where u.organization in (:orgSlug) and  u.deleted_at is null
                                  group by o.slug\s
                                            """,
      nativeQuery = true)
  List<MetricsCountByOrg> getStudentCountByOrgSlug(List<String> orgSlug);

  @Query(
      value =
          """
                  select count(s.*)
                  from  students s\s
                  join users u on s.user_id = u.id
                  join orgs o on o.slug = u.organization\s
                  where o.deleted_at is null and u.deleted_at is null and s.deleted_at is null\s

          """,
      nativeQuery = true)
  Integer getStudentCountofAllOrgs();

  @Query(
      value =
          """
          select sum(round(extract(epoch from end_time)) - round(extract(epoch from start_time)))/60/60
          from exams e
          inner join students s on e.student_id = s.id
          inner join users u on s.user_id = u.id
          where end_time is not null and u.organization = :orgSlug
          """,
      nativeQuery = true)
  Integer countByStudentTime(String orgSlug);

  @Query(
      value =
          """
          SELECT
          	distinct u.*
          FROM
          	users u
          LEFT JOIN user_roles ur ON
          	u.id = ur.user_id
          LEFT JOIN roles r ON
          	ur.role_id = r.id
          WHERE
          	r."name" NOT IN ('ROLE_STUDENT', 'ROLE_ISTUDENT')
          	AND u.email LIKE :email
          LIMIT 1\
          """,
      nativeQuery = true)
  User findFirstUserByEmail(String email);

  @Query(
      value =
          """
          SELECT
          	u.*
          FROM
          	users u
          INNER JOIN user_roles ur ON\s
          	ur.user_id = u.id
          LEFT JOIN roles r ON\s
          	r.id = ur.role_id
          WHERE
          	u.deleted_at IS NULL
          	AND\s
          r."name" LIKE :roleName
          	AND\s
          u.organization LIKE :orgSlug
          ORDER BY
          	RANDOM()
          LIMIT 1\
          """,
      nativeQuery = true)
  User getRandomUserByOrganizationAndRole(String roleName, String orgSlug);

  @Query(
      value =
          """
          SELECT u.organization as orgSlug, o.name as orgName,
                     sum(CASE WHEN r.name = 'ROLE_ISTUDENT' THEN 1 else 0 end) as studentsStrength,
                     sum(CASE WHEN r.name = 'ROLE_ITEACHER' THEN 1 else 0 end) as teachersStrength
              FROM users u
                       inner join public.user_roles ur on ur.user_id = u.id
                       inner join public.roles r on r.id = ur.role_id
              inner join orgs o on o.slug = u.organization
              where u.organization = :orgSlug\s
              group by u.organization, o.name\
          """,
      nativeQuery = true)
  OrganizationStrength getOrganizationStrength(String orgSlug);

  @Query(
      value = "select * from users where organization = :orgSlug order by created_at limit 1",
      nativeQuery = true)
  User fetchPrincipalOfOrganization(String orgSlug);

  @Query(
      value =
          """
select u.id from students s
                     inner join users u on s.user_id = u.id
where s.section_id in (:sectionIds) and u.organization = :orgSlug
""",
      nativeQuery = true)
  List<Long> getStudentIdsOfSections(List<Long> sectionIds, String orgSlug);

  @Query(value = "select u from User u left join Student s on s.userInfo = u where s.id  = ?1")
  User findByStudentId(Long studentId);

  List<User> findByAuthUserIdIn(List<String> authIds);

  Optional<User> findByUserNameAndOrganizationAndDeletedAtIsNull(String userName, String orgSlug);

  Optional<User> findByExternalRef(String externalRef);

  long countByOrganization(String orgSlug);

  @Query(
      value =
          """
                  SELECT DISTINCT u.* FROM users u
                  WHERE u.organization = :orgId
                  AND  (CONCAT(u.first_name, ' ', u.last_name) ilike :searchKey or u.user_name ilike :searchKey)
                  """,
      nativeQuery = true)
  List<User> findByOrgSlugAndSearchKey(String orgId, String searchKey);

  List<User> getUserByAuthUserIdIn(List<String> authUserIds);

  List<User> findAllByOrganization(String orgSlug);
}
