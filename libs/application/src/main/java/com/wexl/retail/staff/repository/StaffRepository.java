package com.wexl.retail.staff.repository;

import com.wexl.retail.model.User;
import com.wexl.retail.staff.model.Staff;
import com.wexl.retail.staff.model.StaffDetails;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface StaffRepository extends JpaRepository<Staff, Long> {
  Optional<Staff> findByUser(User staff);

  List<Staff> findByUserIn(List<User> users);

  @Query(
      value =
          """
                  select d."name" as designationName,d.id as designationId,u.id as userId,u.auth_user_id as authUserId,concat(u.first_name,u.last_name) as fullName from staff_details sd
                                               join users u on u.id = sd.user_id
                                               join designations d on d.id = sd.designation_id
                                               where u.organization = :orgSlug
          """,
      nativeQuery = true)
  List<StaffDetails> getDesignationsByOrg(String orgSlug);

  @Query(
      value =
          """
                  select sd.* from staff_details sd
                  join users u on u.id = sd.user_id
                  where u.organization = :orgSlug
          """,
      nativeQuery = true)
  List<Staff> getStaffByOrg(String orgSlug);
}
