package com.wexl.erp.fees.controller;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.service.FeeDueReportService;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/fee-due-reports")
public class FeeDueReportController {

  private final FeeDueReportService feeDueReportService;

  @IsOrgAdmin
  @PostMapping(value = "/csv", produces = "text/csv")
  public void generateFeeDueReportCsv(
      @PathVariable String orgSlug,
      @RequestBody FeeDto.FeeDueReportRequest request,
      HttpServletResponse httpServletResponse) {

    feeDueReportService.generateFeeDueReportCsv(orgSlug, request, httpServletResponse);
  }

  @IsOrgAdmin
  @PostMapping("/data")
  public List<FeeDto.FeeDueReportResponse> generateFeeDueReportData(
      @PathVariable String orgSlug, @RequestBody FeeDto.FeeDueReportRequest request) {

    return feeDueReportService.generateFeeDueReport(orgSlug, request);
  }

  @IsOrgAdmin
  @PostMapping(value = "/past-due/csv", produces = "text/csv")
  public void generatePastDueReportCsv(
      @PathVariable String orgSlug,
      @RequestBody FeeDto.FeeDueReportRequest request,
      HttpServletResponse httpServletResponse) {

    FeeDto.FeeDueReportRequest pastDueRequest =
        new FeeDto.FeeDueReportRequest(
            request.fromYear(),
            request.toYear(),
            request.feeGroupId(),
            request.sectionUuids(),
            request.studentStatus(),
            FeeDto.FeeDueReportType.PAST_DUE);

    feeDueReportService.generateFeeDueReportCsv(orgSlug, pastDueRequest, httpServletResponse);
  }

  @IsOrgAdmin
  @PostMapping(value = "/total-due/csv", produces = "text/csv")
  public void generateTotalDueReportCsv(
      @PathVariable String orgSlug,
      @RequestBody FeeDto.FeeDueReportRequest request,
      HttpServletResponse httpServletResponse) {

    FeeDto.FeeDueReportRequest totalDueRequest =
        new FeeDto.FeeDueReportRequest(
            request.fromYear(),
            request.toYear(),
            request.feeGroupId(),
            request.sectionUuids(),
            request.studentStatus(),
            FeeDto.FeeDueReportType.TOTAL_DUE);

    feeDueReportService.generateFeeDueReportCsv(orgSlug, totalDueRequest, httpServletResponse);
  }

  @IsOrgAdmin
  @PostMapping(value = "/general-fee-due/csv", produces = "text/csv")
  public void generateGeneralFeeDueReportCsv(
      @PathVariable String orgSlug,
      @RequestBody FeeDto.FeeDueReportRequest request,
      HttpServletResponse httpServletResponse) {

    FeeDto.FeeDueReportRequest generalRequest =
        new FeeDto.FeeDueReportRequest(
            request.fromYear(),
            request.toYear(),
            request.feeGroupId(),
            request.sectionUuids(),
            request.studentStatus(),
            FeeDto.FeeDueReportType.GENERAL_FEE_DUE);

    feeDueReportService.generateFeeDueReportCsv(orgSlug, generalRequest, httpServletResponse);
  }
}
