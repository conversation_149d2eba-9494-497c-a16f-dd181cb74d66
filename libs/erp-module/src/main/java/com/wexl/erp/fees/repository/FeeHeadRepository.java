package com.wexl.erp.fees.repository;

import com.wexl.erp.fees.model.*;
import com.wexl.retail.model.Student;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface FeeHeadRepository extends JpaRepository<FeeHead, Long> {

  boolean existsByFeeMasterAndStudentAndFeeType(
      FeeMaster feeMaster, Student student, FeeType feeType);

  List<FeeHead> findAllByFeeMasterAndOrgSlug(FeeMaster feeMaster, String orgSlug);

  List<FeeHead> findAllByStudentIdAndOrgSlug(Long studentId, String orgSlug);

  Optional<FeeHead> findByIdAndOrgSlug(UUID uuid, String orgSlug);

  List<FeeHead> findAllByConcession(Concession concession);

  Long countByFeeTypeIn(List<FeeType> feeTypes);

  Optional<FeeHead> findByIdAndOrgSlugAndStudent(UUID id, String orgSlug, Student student);

  // Custom queries for fee due reports
  @Query(
      "SELECT fh FROM FeeHead fh "
          + "JOIN fh.feeMaster fm "
          + "JOIN fm.feeGroup fg "
          + "WHERE fh.orgSlug = :orgSlug "
          + "AND fh.student.id IN :studentIds "
          + "AND fh.dueDate < :currentDate "
          + "AND fh.dueDate >= :fromDate "
          + "AND fh.dueDate <= :toDate "
          + "AND fh.status IN ('UNPAID', 'PARTIALLY_PAID') "
          + "AND (:feeGroupId IS NULL OR fg.id = :feeGroupId) "
          + "ORDER BY fh.student.id, fh.dueDate")
  List<FeeHead> findPastDueFeeHeads(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("currentDate") LocalDateTime currentDate,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate,
      @Param("feeGroupId") String feeGroupId);

  @Query(
      "SELECT fh FROM FeeHead fh "
          + "JOIN fh.feeMaster fm "
          + "JOIN fm.feeGroup fg "
          + "WHERE fh.orgSlug = :orgSlug "
          + "AND fh.student.id IN :studentIds "
          + "AND fh.dueDate >= :fromDate "
          + "AND fh.dueDate <= :toDate "
          + "AND fh.status IN ('UNPAID', 'PARTIALLY_PAID') "
          + "AND (:feeGroupId IS NULL OR fg.id = :feeGroupId) "
          + "ORDER BY fh.student.id, fh.dueDate")
  List<FeeHead> findTotalDueFeeHeads(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate,
      @Param("feeGroupId") String feeGroupId);

  @Query(
      "SELECT fh FROM FeeHead fh "
          + "JOIN fh.feeMaster fm "
          + "JOIN fm.feeGroup fg "
          + "WHERE fh.orgSlug = :orgSlug "
          + "AND fh.student.id IN :studentIds "
          + "AND fh.dueDate >= :fromDate "
          + "AND fh.dueDate <= :toDate "
          + "AND fh.balanceAmount > 0 "
          + "AND (:feeGroupId IS NULL OR fg.id = :feeGroupId) "
          + "ORDER BY fh.student.id, fh.dueDate")
  List<FeeHead> findGeneralFeeDue(
      @Param("orgSlug") String orgSlug,
      @Param("studentIds") List<Long> studentIds,
      @Param("fromDate") LocalDateTime fromDate,
      @Param("toDate") LocalDateTime toDate,
      @Param("feeGroupId") String feeGroupId);
}
