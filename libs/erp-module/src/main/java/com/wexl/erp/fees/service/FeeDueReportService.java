package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FeeType;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeeTypeRepository;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final FeeTypeRepository feeTypeRepository;

  // Transport fee types - populated from database
  private final Set<String> transportFeeTypeCodes = new HashSet<>();

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    // Initialize transport fee types if not already done
    initializeTransportFeeTypes(orgSlug);

    List<FeeDto.FeeDueReportResponse> reportData = generateFeeDueReport(orgSlug, request);
    generateCsvResponse(reportData, response, request.reportType());
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request) {

    List<Student> students = getStudentsByFilters(orgSlug, request);

    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    return students.stream()
        .map(student -> buildFeeDueReportResponse(student, feeHeadsByStudent.get(student)))
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private void initializeTransportFeeTypes(String orgSlug) {
    if (transportFeeTypeCodes.isEmpty()) {
      List<FeeType> transportFeeTypes = feeTypeRepository.findByOrgSlugAndCodeContainingIgnoreCase(
          orgSlug, "transport");
      transportFeeTypes.addAll(feeTypeRepository.findByOrgSlugAndNameContainingIgnoreCase(
          orgSlug, "transport"));

      transportFeeTypes.forEach(feeType -> transportFeeTypeCodes.add(feeType.getCode()));

      log.info("Initialized {} transport fee types for org: {}", transportFeeTypeCodes.size(), orgSlug);
    }
  }

  private List<Student> getStudentsByFilters(String orgSlug, FeeDto.FeeDueReportRequest request) {
    List<UUID> sectionUuids =
        request.sectionUuids().stream().map(UUID::fromString).collect(Collectors.toList());

    List<Section> sections = sectionRepository.findAllByUuidIn(sectionUuids);

    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);

      if ("Active".equalsIgnoreCase(request.studentStatus())) {
        sectionStudents =
            sectionStudents.stream()
                .filter(student -> student.getActive() == '1')
                .collect(Collectors.toList());
      } else if ("Inactive".equalsIgnoreCase(request.studentStatus())) {
        sectionStudents =
            sectionStudents.stream()
                .filter(student -> student.getActive() != '1')
                .collect(Collectors.toList());
      }

      students.addAll(sectionStudents);
    }

    return students;
  }

  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    LocalDateTime fromDate =
        Instant.ofEpochMilli(request.fromYear()).atZone(ZoneOffset.UTC).toLocalDateTime();
    LocalDateTime toDate =
        Instant.ofEpochMilli(request.toYear()).atZone(ZoneOffset.UTC).toLocalDateTime();
    LocalDateTime currentDate = LocalDateTime.now();

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());

    return switch (request.reportType()) {
      case PAST_DUE ->
          feeHeadRepository.findPastDueFeeHeads(
              orgSlug, studentIds, currentDate, fromDate, toDate, request.feeGroupId());
      case TOTAL_DUE ->
          feeHeadRepository.findTotalDueFeeHeads(
              orgSlug, studentIds, fromDate, toDate, request.feeGroupId());
    };
  }

  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    User userInfo = student.getUserInfo();
    String studentName =
        (userInfo != null)
            ? userInfo.getFirstName()
                + " "
                + (userInfo.getLastName() != null ? userInfo.getLastName() : "")
            : "Unknown";

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(studentName)
        .admissionNumber(getAdmissionNumber(student))
        .rollNumber(student.getRollNumber())
        .className(student.getSection() != null ? student.getSection().getGradeName() : "")
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(formatDate(student.getCreatedAt()))
        .feeDetails(feeDetails)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getName())
        .feeTypeCode(feeHead.getFeeType().getCode())
        .month(getMonthFromDueDate(feeHead.getDueDate()))
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private String getAdmissionNumber(Student student) {
    if (student.getUserInfo() != null && student.getUserInfo().getUserName() != null) {
      return student.getUserInfo().getUserName();
    }
    return student.getRollNumber();
  }

  private String formatDate(LocalDateTime dateTime) {
    if (dateTime == null) return "";
    return dateTime.format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
  }

  private String formatDate(java.sql.Timestamp timestamp) {
    if (timestamp == null) return "";
    return timestamp.toLocalDateTime().format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
  }

  private String getMonthFromDueDate(LocalDateTime dueDate) {
    if (dueDate == null) return "";
    return dueDate.format(DateTimeFormatter.ofPattern("MMM")).toUpperCase();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      FeeDto.FeeDueReportType reportType) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv");
    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

    String[] csvHeaders = getCsvHeaders();
    List<List<String>> csvBody = buildCsvBody(reportData);

    CsvUtils.generateCsv(csvHeaders, csvBody, response);
  }

  private String getFileName(FeeDto.FeeDueReportType reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    return switch (reportType) {
      case PAST_DUE -> "past_due_report_" + timestamp + ".csv";
      case TOTAL_DUE -> "total_due_report_" + timestamp + ".csv";
      case GENERAL_FEE_DUE -> "general_fee_due_report_" + timestamp + ".csv";
    };
  }

  private String[] getCsvHeaders() {
    return new String[] {
      "Student Name",
      "Admission Number",
      "Roll Number",
      "Class",
      "Date of Admission",
      "APR",
      "MAY",
      "JUN",
      "JUL",
      "AUG",
      "SEP",
      "OCT",
      "NOV",
            "DEC",
            "JAN",
            "FEB",
            "MAR",
      "Transport Fee",
      "Total Due"
    };
  }

  private List<List<String>> buildCsvBody(List<FeeDto.FeeDueReportResponse> reportData) {
    List<List<String>> csvBody = new ArrayList<>();

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = new ArrayList<>();

      row.add(report.studentName());
      row.add(report.admissionNumber());
      row.add(report.rollNumber());
      row.add(report.className());
      row.add(report.dateOfAdmission());

      Map<String, Double> monthlyFees = new HashMap<>();
      String[] months = {"APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR"};
      for (String month : months) {
        monthlyFees.put(month, 0.0);
      }

      Double transportFee = 0.0;

      for (FeeDto.FeeDetailResponse feeDetail : report.feeDetails()) {
        if (transportFeeTypeCodes.contains(feeDetail.feeTypeCode())) {
          transportFee += feeDetail.balanceAmount();
        } else if (monthlyFees.containsKey(feeDetail.month())) {
          monthlyFees.put(
              feeDetail.month(), monthlyFees.get(feeDetail.month()) + feeDetail.balanceAmount());
        }
      }

      for (String month : months) {
        Double amount = monthlyFees.get(month);
        row.add(amount > 0 ? "₹ " + String.format("%.0f", amount) : "₹ 0");
      }

      row.add(transportFee > 0 ? "₹ " + String.format("%.0f", transportFee) : "₹ 0");

      csvBody.add(row);
    }

    return csvBody;
  }
}
